variables:
  allStages: true

trigger: none

stages:
  ### WEU
  - stage: nginx_ece_sbod_ece_1_v1_weu
    displayName: Deploy SBOD NGINX DEV on WEU
    jobs:
      - template: "../templates/nginx-ingress/nginx-deploy-job.yaml"
        parameters:
          jobName: job_deploy_ngnix_ece_dev_1_v1_weu
          subscription: "ECE-DEV-USER-ASSIGNED-MANAGED-IDENTITY-SBOD"
          subscriptionVault: "ECE-DEV-DEVWEUB-SBOD"
          aksClusterResourceGroup: "SBOD-DEV-K8S-V1-westeurope"
          aksClusterName: "sbod_ece_1_v1_weu"
          networkResourceGroup: "Network-SBOD-Services-PublicIPs"
          nginxNamespace: "ingress-nginx"
          nginxHelmReleaseName: "ingress-nginx"
          nginxChartName: "ingress-nginx/ingress-nginx"
          nginxChartVersion: "4.7.1"
          nginxChartValueFile: "$(System.DefaultWorkingDirectory)/helm-values/nginx-ingress/other/config.yaml"
          deployProceed: true
