variables:
  allStages: true

trigger: none

stages:
  ### WEU
  - stage: datadog_ece_sbod_ece_1_v1_weu
    displayName: Deploy SBOD NGINX NONPROD on WEU
    jobs:
      - template: "../templates/nginx-ingress/nginx-deploy-job.yaml"
        parameters:
          jobName: job_deploy_ngnix_ece_nprod_1_v1_weu
          subscription: "ECE-NPRD-NPRWEUB-SBOD"
          subscriptionVault: "ECE-NPRD-NPRWEUB-SBOD"
          aksClusterResourceGroup: "SBOD-NONPROD-K8S-V1-westeurope"
          aksClusterName: "sbod_ece_1_v1_weu"
          nginxNamespace: "ingress-nginx"
          nginxHelmReleaseName: "ingress-nginx"
          nginxChartName: "ingress-nginx/ingress-nginx"
          nginxChartVersion: "4.7.1"
          nginxChartValueFile: "$(System.DefaultWorkingDirectory)/helm-values/nginx-ingress/other/config.yaml"
          networkResourceGroup: "Network-SBOD-Services-PublicIPs"
          deployProceed: true
