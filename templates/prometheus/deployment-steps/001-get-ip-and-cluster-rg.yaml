parameters:
  - name: subscription
  - name: cluster_name
  - name: value_file
  - name: network_rg

steps:
  - task: AzureCLI@1
    displayName: 'Get IP and Cluster RG'
    inputs:
      azureSubscription: ${{ parameters.subscription }}
      scriptPath: '$(System.DefaultWorkingDirectory)/scripts/getIPandFQDN.sh'
      arguments: '${{ parameters.cluster_name }} ${{ parameters.network_rg }}' 
      workingDirectory: '$(System.DefaultWorkingDirectory)/scripts'
      failOnStandardError: true

    # Change the publicIP/FQDN/Heartbeat in the prometheus config file
  - bash: |
      CLNM=${{ parameters.cluster_name }}
      OPGE=$(echo $CLNM | awk '{ print toupper($0) }' | sed -e "s/_/-/g" | cut -f1-5 -d "-")
      echo "========================================"
      echo "OpsGenie heartbeat name: $OPGE"
      
      # Fix the file path - remove duplicate path components
      VALUE_FILE="${{ parameters.value_file }}"
      if [[ "$VALUE_FILE" == /* ]]; then
          # If value_file starts with /, use it as absolute path
          FILE_PATH="$VALUE_FILE"
      else
          # If value_file is relative, prepend System.DefaultWorkingDirectory
          FILE_PATH="$(System.DefaultWorkingDirectory)/$VALUE_FILE"
      fi
      
      echo "Working with file: $FILE_PATH"
      
      # Check if file exists before processing
      if [ ! -f "$FILE_PATH" ]; then
          echo "ERROR: File not found: $FILE_PATH"
          echo "Available files in directory:"
          ls -la $(System.DefaultWorkingDirectory)/
          exit 1
      fi
      
      sed -ie 's/111.222.333.444/$(pubIP)/g' "$FILE_PATH"
      sed -ie 's/CLUSTERFQDN/$(ClusterFQDN)/g' "$FILE_PATH"
      sed -ie "s/__OPSGENIE__/$OPGE/g" "$FILE_PATH"
      echo "========================================"
      echo "IP Checks in the config file:"
      cat "$FILE_PATH" | grep ":443"
      echo "========================================"
      echo "Heartbeat name check in the config file:"
      cat "$FILE_PATH" | grep "$OPGE"
      echo "========================================"