parameters:
  - name: jobName
  - name: jobDisplayName
    default: 'Deploy Prometheus'
  - name: vmImage
    default: 'ubuntu-latest'
  - name: helmVersion
    default: '3.14.0'
  - name: subscription
    default: 'ADO-IAMS-DaiVB IAM Services'
  - name: subscriptionVault
    default: 'ADO-IAMS-DaiVB IAM Services'

  - name: aksClusterResourceGroup
  - name: aksClusterName

  - name: monPromNamespace
  - name: monPromHelmReleaseName
  - name: monPromChartName
  - name: monPromChartVersion
  - name: monPromChartValueFile

  - name: monBlackNamespace
  - name: monBlackHelmReleaseName
  - name: monBlackChartName
  - name: monBlackChartVersion
  - name: monBlackChartValueFile

  - name: networkResourceGroup

  - name: deployProceed
    type: boolean
    default: false

jobs:
  - job: ${{ parameters.jobName }}
    displayName: ${{ parameters.jobDisplayName }}
    pool:
      vmImage: ${{ parameters.vmImage }}
    steps:
      - checkout: self
        persistCredentials: true

      - template: deployment-steps/001-get-ip-and-cluster-rg.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_name: ${{ parameters.aksClusterName }}
          value_file: ${{ parameters.monPromChartValueFile }}
          network_rg: ${{ parameters.networkResourceGroup }}

      - template: deployment-steps/002-install-helm.yaml
        parameters:
          version: ${{ parameters.helmVersion }}

      - template: deployment-steps/003-install-kubectl.yaml

      - template: deployment-steps/004-add-helm-repo.yaml

      - template: deployment-steps/005-create-monitoring-namespace.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}

      - template: deployment-steps/006-helm-upgrade-monitoring.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.monPromNamespace }}
          release_name: ${{ parameters.monPromHelmReleaseName }}
          chart_name: ${{ parameters.monPromChartName }}
          chart_version: ${{ parameters.monPromChartVersion }}
          value_file: ${{ parameters.monPromChartValueFile }}

      - template: deployment-steps/007-helm-upgrade-blackbox.yaml
        parameters:
          subscription: ${{ parameters.subscription }}
          cluster_rg: ${{ parameters.aksClusterResourceGroup }}
          cluster_name: ${{ parameters.aksClusterName }}
          namespace: ${{ parameters.monBlackNamespace }}
          release_name: ${{ parameters.monBlackHelmReleaseName }}
          chart_name: ${{ parameters.monBlackChartName }}
          chart_version: ${{ parameters.monBlackChartVersion }}
          value_file: ${{ parameters.monBlackChartValueFile }}